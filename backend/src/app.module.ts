import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersModule } from './modules/users';
import { AuthModule } from './modules/auth';
import { BooksModule } from './modules/books';
import { CartsModule } from './modules/carts';
import { OrdersModule } from './modules/orders';
import { PaymentsModule } from '@/modules/payments/payments.module';
import { Book, BookSchema } from '@/modules/books/schemas/book.schema';
import { CommonModule } from './common/common.module';
import { ModulesModule } from './modules/modules.module';
import { InfraModule } from './infra/infra.module';

@Module({
  imports: [
    // Configure environment variables
    ConfigModule.forRoot({
      isGlobal: true, // Make config available throughout the application
      envFilePath: '.env', // Specify the path to the .env file
    }),

    // Configure MongoDB connection
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGO_URI'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
    }),

    MongooseModule.forFeature([{ name: Book.name, schema: BookSchema }]),

    // Feature modules
    UsersModule,
    AuthModule,
    BooksModule,
    CartsModule,
    OrdersModule,
    PaymentsModule,
    CommonModule,
    ModulesModule,
    InfraModule,
  ],
})
export class AppModule {}
