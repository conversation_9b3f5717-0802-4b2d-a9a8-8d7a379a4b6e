import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { Request } from 'express';
import { ApiResponse } from '../types/api-response.type';

@Injectable()
export class RequestInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RequestInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const { method, url } = request;
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.logger.debug(`${method} ${url} - Execution time: ${duration}ms`);
      }),
      map((data) => {
        // Transform response to standardized format if it's not already wrapped
        if (data && typeof data === 'object' && !data.hasOwnProperty('success')) {
          return ApiResponse.success(data, 'Request successful', request.url);
        }
        return data;
      }),
    );
  }
}
