import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import { SwaggerTags } from './swagger-tags.enum';

export class SwaggerConfig {
  static setup(app: INestApplication): void {
    const config = new DocumentBuilder()
      .setTitle('Online Bookstore API')
      .setDescription(
        'Comprehensive API documentation for the Online Bookstore application. ' +
        'This API provides endpoints for user authentication, book management, ' +
        'shopping cart operations, order processing, and payment handling.',
      )
      .setVersion('1.0.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addServer('http://localhost:3000', 'Development server')
      .addServer('http://localhost:4000', 'Alternative development server')
      .addTag(SwaggerTags.AUTH, 'User authentication and authorization')
      .addTag(SwaggerTags.USERS, 'User profile management')
      .addTag(SwaggerTags.BOOKS, 'Book catalog and search')
      .addTag(SwaggerTags.CARTS, 'Shopping cart operations')
      .addTag(SwaggerTags.ORDERS, 'Order management')
      .addTag(SwaggerTags.PAYMENTS, 'Payment processing')
      .addTag(SwaggerTags.ADMIN_USERS, 'Administrative user management')
      .addTag(SwaggerTags.ADMIN_BOOKS, 'Administrative book management')
      .addTag(SwaggerTags.ADMIN_ORDERS, 'Administrative order management')
      .addTag(SwaggerTags.ADMIN_DASHBOARD, 'Administrative dashboard and analytics')
      .addTag(SwaggerTags.HEALTH, 'Application health monitoring')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
      customSiteTitle: 'Online Bookstore API Documentation',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #3b82f6 }
      `,
    });
  }
}
